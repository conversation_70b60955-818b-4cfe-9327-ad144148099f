-- 创建设备表
CREATE TABLE IF NOT EXISTS cmcc_device (
    id VARCHAR(36) PRIMARY KEY,
    device_name VARCHAR(100) NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    last_communication_time TIMESTAMP,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建设备属性表
CREATE TABLE IF NOT EXISTS cmcc_device_property (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    property_name VARCHAR(100) NOT NULL,
    property_value VARCHAR(255),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES cmcc_device(id)
);

-- 创建设备遥测数据表
CREATE TABLE IF NOT EXISTS cmcc_device_telemetry (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    telemetry_name VARCHAR(100) NOT NULL,
    telemetry_value VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES cmcc_device(id)
);

-- 创建设备命令表
CREATE TABLE IF NOT EXISTS cmcc_device_command (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    command_name VARCHAR(100) NOT NULL,
    command_params TEXT,
    status VARCHAR(20) NOT NULL,
    result TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES cmcc_device(id)
);

-- 创建设备事件表
CREATE TABLE IF NOT EXISTS cmcc_device_event (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data TEXT,
    timestamp TIMESTAMP NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES cmcc_device(id)
); 