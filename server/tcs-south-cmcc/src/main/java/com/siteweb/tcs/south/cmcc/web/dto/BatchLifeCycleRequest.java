package com.siteweb.tcs.south.cmcc.web.dto;

import lombok.Data;

import java.util.List;

/**
 * 批量生命周期事件请求DTO
 * 
 * <AUTHOR> (2025-01-15)
 */
@Data
public class BatchLifeCycleRequest {
    
    /**
     * FSU ID列表
     */
    private List<String> fsuIds;
    
    /**
     * 生命周期事件类型
     * 可选值: CREATE, DELETE, FIELD_UPDATE, LOAD, UNLOAD, RELOAD, START, STOP, RESTART
     */
    private String eventType;
}
