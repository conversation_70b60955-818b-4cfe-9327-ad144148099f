package com.siteweb.tcs.siteweb.util;


import org.apache.tomcat.util.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class EncryptUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(EncryptUtil.class);

    /*
     * @param inputStr 待加密的字符串
     * @return 加密后的字符串
     * SHA1加密
     * */
    public static String sha1(String inputStr) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(inputStr.getBytes(StandardCharsets.UTF_8));
            byte[] messageDigest = digest.digest();
            //Create Hex String
            StringBuilder stringBuilder = new StringBuilder();
            //字节数组转换为16进制
            for (int i = 0; i < messageDigest.length; i++) {
                String toHexString = Integer.toHexString(messageDigest[i] & 0xFF);
                if (toHexString.length() < 2) {
                    stringBuilder.append(0);
                }
                stringBuilder.append(toHexString);
            }
            return stringBuilder.toString();
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("Sha1 encrypt password error!");
        }
        return "";
    }

    /*
     * @param inputStr 待加密的字符串
     * @return 加密后的字符串
     * SHA256加密
     * */
    public static String sha256(String inputStr) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(inputStr.getBytes(StandardCharsets.UTF_8));
            byte[] messageDigest = digest.digest();
            //Create Hex String
            StringBuilder stringBuilder = new StringBuilder();
            //字节数组转换为16进制
            for (int i = 0; i < messageDigest.length; i++) {
                String toHexString = Integer.toHexString(messageDigest[i] & 0xFF);
                if (toHexString.length() < 2) {
                    stringBuilder.append(0);
                }
                stringBuilder.append(toHexString);
            }
            return stringBuilder.toString();
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("Sha256 encrypt password error!");
        }
        return "";
    }

    /**
     * 将16进制密文按每2位转为10进制
     *
     * @param pwd 16进制密文，其长度须为64
     * @return 10进制密文
     */
    public static String changePasswordFormat(String pwd) {
        int xPwdLength = 64;
        if (pwd.length() != xPwdLength) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int tmpValue;
        for (int i = 0; i < xPwdLength; i += 2) {
            tmpValue = Integer.parseInt(pwd.substring(i, i + 2), 16);
            sb.append(tmpValue);
        }
        return sb.toString();
    }

    private EncryptUtil() {
        //default constructor
    }






    //参数分别代表 算法名称/加密模式/数据填充方式
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";

    public static final  String DEFAULT_API_KEY = "X1Y2Z3A4B5C6D7E8F9G0H1I2J3K4L5M6";




    /**
     * 加密
     * @param content 加密的字符串
     * @param encryptKey key值
     * @return
     * @throws Exception
     */
    public static String encrypt(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
        byte[] b = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        // 采用base64算法进行转码,避免出现中文乱码
        return Base64.encodeBase64String(b);

    }



    /**
     * 解密
     * @param encryptStr 解密的字符串
     * @param decryptKey 解密的key值
     * @return
     * @throws Exception
     */
    public static String decrypt(String encryptStr, String decryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
        // 采用base64算法进行转码,避免出现中文乱码
        byte[] encryptBytes = Base64.decodeBase64(encryptStr);
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes);
    }




}
