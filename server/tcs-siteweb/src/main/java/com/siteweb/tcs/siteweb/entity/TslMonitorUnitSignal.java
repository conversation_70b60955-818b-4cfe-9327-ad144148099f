package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控单元信号实体类
 */
@Data
@TableName("tsl_monitorunitsignal")
public class TslMonitorUnitSignal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @TableField("monitorUnitId")
    private Integer monitorUnitId;

    @TableField("stationId")
    private Integer stationId;

    @TableField("signalName")
    private String signalName;

    @TableField("signalCode")
    private String signalCode;

    @TableField("dataType")
    private String dataType;

    @TableField("description")
    private String description;
} 