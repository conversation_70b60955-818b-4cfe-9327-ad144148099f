package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.constants.SignalConstant;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.entity.Signal;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import com.siteweb.tcs.siteweb.entity.SignalProperty;
import com.siteweb.tcs.siteweb.enums.*;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.SignalMapper;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.ISignalService;
import com.siteweb.tcs.siteweb.service.ISignalMeaningsService;
import com.siteweb.tcs.siteweb.service.ISignalPropertyService;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.TokenUserSiteWebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Signal Service Implementation
 */
@Slf4j
@Service
public class SignalServiceImpl extends ServiceImpl<SignalMapper, Signal> implements ISignalService {

    @Autowired
    private SignalMapper signalMapper;

    @Autowired(required = false)
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Autowired(required = false)
    private ISignalMeaningsService signalMeaningsService;

    @Autowired(required = false)
    private ISignalPropertyService signalPropertyService;

    @Autowired
    private I18n i18n;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Signal createSignal(SignalConfigItem signalConfigItem) {
        // Create signal entity from DTO
        Signal signal = new Signal();
        BeanUtils.copyProperties(signalConfigItem, signal, "id", "signalId");

        // Generate signal ID if needed
        if (signal.getSignalId() == null || signal.getSignalId() == -99999) {
            // Find max signal ID for this equipment template
            Integer signalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
            signal.setSignalId(signalId);
        }

        // Validate signal name - check for duplicates
        List<Signal> existingSignals = findByEquipmentTemplateId(signal.getEquipmentTemplateId());
        if (!CollectionUtils.isEmpty(existingSignals)) {
            for (Signal existingSignal : existingSignals) {
                // Check for duplicate signal name
                if (Objects.equals(existingSignal.getSignalName(), signal.getSignalName())) {
                    throw new RuntimeException("Signal name already exists: " + signal.getSignalName());
                }

                // Check for duplicate channel number
                // Channel numbers > 0 or equal to -3 must be unique
                if ((signal.getChannelNo() > 0 || signal.getChannelNo() == -3) &&
                    Objects.equals(existingSignal.getChannelNo(), signal.getChannelNo())) {
                    throw new RuntimeException("Channel number already exists: " + signal.getChannelNo());
                }
            }
        }

        // Set default module number if not provided
        if (signal.getModuleNo() == null) {
            signal.setModuleNo(0);
        }

        // Set default values for enable/visible if not provided
        if (signal.getEnable() == null) {
            signal.setEnable(true);
        }
        if (signal.getVisible() == null) {
            signal.setVisible(true);
        }

        // Save the signal
        boolean success = save(signal);
        if (!success) {
            throw new RuntimeException("Failed to save signal");
        }

        // Create signal meanings if provided
        if (signalMeaningsService != null && !CollectionUtils.isEmpty(signalConfigItem.getSignalMeaningsList())) {
            List<SignalMeanings> meaningsList = signalConfigItem.getSignalMeaningsList();
            // Set signal ID for each meaning
            meaningsList.forEach(meaning -> meaning.setSignalId(signal.getSignalId()));
            signalMeaningsService.batchCreateSignalMeanings(meaningsList);
        }

        // Create signal properties if provided
        if (signalPropertyService != null && !CollectionUtils.isEmpty(signalConfigItem.getSignalPropertyList())) {
            List<SignalProperty> propertyList = signalConfigItem.getSignalPropertyList();
            // Set signal ID for each property
            propertyList.forEach(property -> property.setSignalId(signal.getSignalId()));
            signalPropertyService.batchCreateSignalProperty(propertyList);
        }

        // Update the DTO with generated values
        signalConfigItem.setId(signal.getId());
        signalConfigItem.setSignalId(signal.getSignalId());

        // Record operation log
        if (operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        TokenUserSiteWebUtil.getLoginUserId(), // User ID
                        signal.getSignalId().toString(),
                        OperationObjectTypeEnum.SIGNAL,
                        "signal.signalName",
                        "添加",
                        "",
                        signal.getSignalName()
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        return signal;
    }

    /**
     * Find the maximum signal ID for an equipment template and increment it
     */
    public Integer findMaxSignalIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxSignalId = signalMapper.findMaxSignalIdByEquipmentTemplateId(equipmentTemplateId);
        if (maxSignalId != null) {
            return ++maxSignalId;
        }
        // If no signals exist yet, get a global ID from the primary key service
        if (primaryKeyValueService != null) {
            return primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_SIGNAL, 0);
        }
        // Fallback to starting value if service is not available
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Signal updateSignal(SignalConfigItem signalConfigItem) {
        if (signalConfigItem == null || signalConfigItem.getSignalId() == null
                || signalConfigItem.getEquipmentTemplateId() == null) {
            throw new IllegalArgumentException("Signal ID and equipment template ID cannot be null");
        }

        // Check if signal exists
        SignalConfigItem oldSignal = findByEquipmentTemplateIdAndSignalId(
                signalConfigItem.getEquipmentTemplateId(), signalConfigItem.getSignalId());
        if (oldSignal == null) {
            throw new RuntimeException("Signal not found");
        }

        // Validate signal name
        if (existsByNameInTemplate(signalConfigItem.getEquipmentTemplateId(),
                signalConfigItem.getSignalName(), signalConfigItem.getSignalId())) {
            throw new RuntimeException("Signal name already exists in the equipment template");
        }

        // Create signal entity from DTO
        Signal signal = new Signal();
        BeanUtils.copyProperties(signalConfigItem, signal);

        // Record operation log
        if (operationDetailService != null) {
            try {
                operationDetailService.compareEntitiesRecordLog(
                        null, // User ID
                        oldSignal,
                        signal
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        // Update signal
        boolean success = updateById(signal);
        if (!success) {
            throw new RuntimeException("Failed to update signal");
        }

        return getById(signal.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSignal(Integer equipmentTemplateId, Integer signalId) {
        if (equipmentTemplateId == null || signalId == null) {
            return 0;
        }

        // Check if signal exists
        SignalConfigItem signal = findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
        if (signal == null) {
            return 0;
        }

        // Delete signal using Lambda query wrapper (using standard MyBatis-Plus method)
        LambdaQueryWrapper<Signal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                    .eq(Signal::getSignalId, signalId);

        boolean success = remove(queryWrapper);

        // Record operation log
        if (success && operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        null, // User ID
                        signal.getSignalId().toString(),
                        OperationObjectTypeEnum.SIGNAL,
                        "signal.signalName",
                        "删除",
                        signal.getSignalName(),
                        ""
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        return success ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteSignal(Integer equipmentTemplateId, List<Integer> signalIds) {
        if (equipmentTemplateId == null || CollectionUtils.isEmpty(signalIds)) {
            return 0;
        }

        // Find signals first for logging
        List<Signal> signals = findSignalsByIdsForLogging(equipmentTemplateId, signalIds);

        if (CollectionUtils.isEmpty(signals)) {
            return 0;
        }

        // Use custom mapper method for batch delete
        int deletedCount = signalMapper.batchDelete(equipmentTemplateId, signalIds);

        // Record operation logs
        if (deletedCount > 0 && operationDetailService != null) {
            for (Signal signal : signals) {
                try {
                    operationDetailService.recordOperationLog(
                            null, // User ID
                            signal.getSignalId().toString(),
                            OperationObjectTypeEnum.SIGNAL,
                            "signal.signalName",
                            "删除",
                            signal.getSignalName(),
                            ""
                    );
                } catch (Exception e) {
                    log.warn("Failed to record operation log for signal ID {}", signal.getSignalId(), e);
                }
            }
        }

        return deletedCount;
    }

    /**
     * Helper method to find signals by IDs for logging
     */
    private List<Signal> findSignalsByIdsForLogging(Integer equipmentTemplateId, List<Integer> signalIds) {
        LambdaQueryWrapper<Signal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                    .in(Signal::getSignalId, signalIds);
        return list(queryWrapper);
    }

    @Override
    public List<Signal> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            return new ArrayList<>();
        }

        // Use custom mapper method
        return signalMapper.findByTemplateId(equipmentTemplateId);
    }

    @Override
    public SignalConfigItem findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        if (equipmentTemplateId == null || signalId == null) {
            return null;
        }

        Signal signal = signalMapper.findSignalEntityByTemplateIdAndSignalId(equipmentTemplateId, signalId);
        if (signal == null) {
            return null;
        }

        SignalConfigItem signalConfigItem = new SignalConfigItem();
        BeanUtils.copyProperties(signal, signalConfigItem);

        if (signalMeaningsService != null) {
            List<SignalMeanings> meaningsList = signalMeaningsService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
            signalConfigItem.setSignalMeaningsList(meaningsList);
        }

        if (signalPropertyService != null) {
            List<SignalProperty> propertyList = signalPropertyService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
            signalConfigItem.setSignalPropertyList(propertyList);
        }

        // The acrossSignal flag might need to be set here if logic for it exists.
        // For now, it will default to null or whatever BeanUtils.copyProperties does.
        // Original siteweb6-config-server's SignalServiceImpl.findItemByEquipmentTemplateIdAndEquipmentId had logic for this.
        // If that's needed here, we'd need to replicate that, potentially fetching from TslAcrossMonitorUnitSignalMapper.

        return signalConfigItem;
    }

    @Override
    public boolean existsByNameInTemplate(Integer equipmentTemplateId, String signalName, Integer signalId) {
        if (equipmentTemplateId == null || signalName == null || signalName.isEmpty()) {
            return false;
        }

        LambdaQueryWrapper<Signal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                    .eq(Signal::getSignalName, signalName);

        // If signalId is provided, exclude that signal from the check
        if (signalId != null) {
            queryWrapper.ne(Signal::getSignalId, signalId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongSignal() {
        // TODO: Implement batch save lian tong signal logic
        log.warn("batchsaveLianTongSignal method not implemented yet");
        return true;
    }

    @Override
    public void updateWorkStationSignalName(String workStationName, Integer equipmentTemplateId) {
        if (workStationName == null || equipmentTemplateId == null) {
            throw new IllegalArgumentException("WorkStationName and equipmentTemplateId cannot be null");
        }
        
        log.info("Updating work station signal names with prefix: {} for equipmentTemplateId: {}", 
                workStationName, equipmentTemplateId);
        
        signalMapper.updateWorkStationSignalName(workStationName, equipmentTemplateId);
    }

    @Override
    public void updateDBWorkStationSignalName(String signalName, Integer equipmentTemplateId, Integer workStationId) {
        if (signalName == null || equipmentTemplateId == null || workStationId == null) {
            throw new IllegalArgumentException("SignalName, equipmentTemplateId and workStationId cannot be null");
        }

        log.info("Updating signal name to: {} for equipmentTemplateId: {} and signalId: {}",
                signalName, equipmentTemplateId, workStationId);

        // Use UpdateWrapper to update specific signal name
        UpdateWrapper<Signal> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("SignalName", signalName)
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", workStationId);

        boolean success = update(updateWrapper);
        if (!success) {
            throw new RuntimeException("Failed to update signal name");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSelfDiagnosisSignal(Integer equipmentTemplateId, Integer centerId) {
        if (equipmentTemplateId == null || centerId == null) {
            throw new IllegalArgumentException("EquipmentTemplateId and centerId cannot be null");
        }
        
        log.info("Updating self diagnosis signal IDs for equipmentTemplateId: {} with centerId: {}", 
                equipmentTemplateId, centerId);
        
        signalMapper.updateSelfDiagnosisSignal(equipmentTemplateId, centerId);
    }

    @Override
    public SignalConfigItem findMaxSignalByEquipmentTemplateId(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            throw new IllegalArgumentException("EquipmentTemplateId cannot be null");
        }
        
        log.debug("Finding maximum signal by equipmentTemplateId: {}", equipmentTemplateId);
        
        return signalMapper.findMaxSignalByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    public void createCommunicationStateSignal(Integer equipmentTemplateId) {
        if (existCommunicationStateSignal(equipmentTemplateId)) {
            return;
        }
        Signal communicationStateSignal = new Signal();
        communicationStateSignal.setEquipmentTemplateId(equipmentTemplateId);
        communicationStateSignal.setSignalId(SignalConstant.COMMUNICATION_STATE_SIGNAL);
        communicationStateSignal.setSignalName(i18n.T("monitor.equipment.communicationState"));
        communicationStateSignal.setSignalCategory(SignalCategoryEnum.SWITCH_SIGNAL.getValue());
        communicationStateSignal.setSignalType(SignalTypeEnum.VIRTUAL_SIGNAL.getValue());
        communicationStateSignal.setChannelNo(SignalConstant.COMMUNICATION_STATE_SIGNAL);
        communicationStateSignal.setChannelType(ChannelTypeEnum.ANALOG.getValue());
        communicationStateSignal.setDataType(DataTypeEnum.FLOAT.getValue());
        communicationStateSignal.setShowPrecision("0");
        communicationStateSignal.setEnable(true);
        communicationStateSignal.setVisible(true);
        communicationStateSignal.setModuleNo(0);
        communicationStateSignal.setDisplayIndex(findCurrentDisplayIndexByEquipmentTemplateId(equipmentTemplateId));
        
        // Create signal using direct save instead of createSignal to avoid validation conflicts
        createSignal(communicationStateSignal);
        
        //设备通信状态的信号属性
        if (signalPropertyService != null) {
            SignalProperty signalProperty = new SignalProperty();
            signalProperty.setEquipmentTemplateId(equipmentTemplateId);
            signalProperty.setSignalId(SignalConstant.COMMUNICATION_STATE_SIGNAL);
            signalProperty.setSignalPropertyId(27);
            signalPropertyService.createSignalProperty(signalProperty);
        }
        
        //设备通信状态的信号含义
        if (signalMeaningsService != null) {
            signalMeaningsService.communicationStateSignalMeaning(equipmentTemplateId);
        }
    }

    /**
     * 是否存在设备通信状态信号
     * 注：设备通信状态信号id是固定的一直是 -3
     *
     * @return true是  false否
     */
    private boolean existCommunicationStateSignal(Integer equipmentTemplateId) {
        return count(Wrappers.lambdaQuery(Signal.class)
                .eq(Signal::getEquipmentTemplateId, equipmentTemplateId)
                .eq(Signal::getSignalId, SignalConstant.COMMUNICATION_STATE_SIGNAL)) > 0;
    }

    /**
     * 根据设备模板取得信号当前的DisplayIndex
     *
     * @param equipmentTemplateId 模板id
     * @return 该模板中最大的DisplayIndex
     */
    private Integer findCurrentDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxDisplayIndex = signalMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);
        if (maxDisplayIndex == null) {
            return 1;
        }
        return ++maxDisplayIndex;
    }

    @Override
    public List<Long> findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return signalMapper.findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    public void createSignal(Signal signal) {
        if (Objects.isNull(signal.getSignalId()) || Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
            List<SignalConfigItem> signalConfigItems = signalMapper.findSignalItemByEquipmentTemplateId(signal.getEquipmentTemplateId());
            // 信号名称不能重复、通道号 channelNo 不可为空值 大于0 时、-3 不可重复，小于等于0 可重复
            if (CollUtil.isNotEmpty(signalConfigItems)) {
                for (SignalConfigItem signalConfigItem : signalConfigItems) {
                    if (Objects.equals(signalConfigItem.getSignalName(), signal.getSignalName())) {
                        throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("monitor.signal"), signal.getSignalName()));
                    }
                    if ((signal.getChannelNo() > 0 || signal.getChannelNo() == -3) && Objects.equals(signalConfigItem.getChannelNo(), signal.getChannelNo())) {
                        throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("signal.channelNo"), signal.getChannelNo()));
                    }
                }
            }
            Integer signalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
            signal.setSignalId(signalId);
        }
        signalMapper.insert(signal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertSignal(List<Signal> signalList) {
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        Integer maxSignalId = null;
        for (Signal signal : signalList) {
            // insertBatchSomeColumn不允许字段为null，如果ModuleNo为null，默认设置为0。
            if (Objects.isNull(signal.getModuleNo())) {
                signal.setModuleNo(0);
            }
            if (Objects.nonNull(signal.getSignalId()) && !Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
                continue;
            }
            if (Objects.isNull(maxSignalId)) {
                maxSignalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
                signal.setSignalId(maxSignalId);
                continue;
            }
            signal.setSignalId(++maxSignalId);
        }
        saveBatch(signalList);
    }
}
