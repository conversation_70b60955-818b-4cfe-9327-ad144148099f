package com.siteweb.tcs.tracking.connector.letter;

import com.siteweb.tcs.common.o11y.WindowLogItem;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 埋点数据消息
 * 用于在Actor之间传递埋点数据
 */
@Data
public class TrackingDataMessage implements WindowLogItem {
    
    /**
     * 策略ID
     */
    private Integer strategyId;
    
    /**
     * 埋点点位ID
     */
    private Integer pointId;
    
    /**
     * 埋点编码
     */
    private String pointCode;
    
    /**
     * 埋点时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 数据来源ID
     */
    private String sourceId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 埋点数据
     */
    private Map<String, Object> data;
    
    /**
     * 获取日志字符串
     */
    @Override
    public String getWindowLogString() {
        return String.format(
                "TrackingData[strategy=%d, point=%d, code=%s, source=%s, user=%s, session=%s, time=%s]",
                strategyId, pointId, pointCode, sourceId, userId, sessionId, timestamp
        );
    }
}
