package com.siteweb.stream.core.runtime;

import com.siteweb.stream.common.annotations.Traceable;
import com.siteweb.stream.common.extensions.ShapeExtension;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.runtime.StreamTraceContext;
import com.siteweb.stream.common.runtime.TraceableValue;
import org.apache.pekko.actor.ActorSystem;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-02-25)
 **/
public class StreamGraphTracer implements StreamTraceContext {
    private final List<SseEmitter> clients = new ArrayList<>();
    private final ActorSystem system;

    public StreamGraphTracer(ActorSystem system) {
        this.system = system;
    }


    public SseEmitter connect() throws IOException {
        SseEmitter emitter = new SseEmitter();
        emitter.onCompletion(() -> clients.remove(emitter));
        emitter.onTimeout(() -> clients.remove(emitter));
        emitter.onError(e -> clients.remove(emitter));
        sendInitMessage(emitter, system);
        clients.add(emitter);
        return emitter;
    }

    private void sendInitMessage(SseEmitter emitter, ActorSystem system) throws IOException {
        List<TraceableValue> traceValues = new ArrayList<>();
        // collect list
        List<AbstractShape> shapes = ShapeExtension.INSTANCE.get(system).getGraphAllShape(10000L);
        for (var shape : shapes) {
            copyTraceable(shape.getClass(), shape, traceValues);
        }
        var data = SseEmitter.event().name("init").data(traceValues);
        emitter.send(data);
    }


    private void copyTraceable(Class<?> clazz, Object source, List<TraceableValue> target) {
        for (Field field : clazz.getDeclaredFields()) {
            // 检查字段是否包含 @Recoverable 注解
            if (field.isAnnotationPresent(Traceable.class)) {
                try {
                    // 设置可访问性
                    field.setAccessible(true);
                    Object value = field.get(source);
                    if (value instanceof TraceableValue traceValue) {
                        target.add(traceValue);
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        var superClazz = clazz.getSuperclass();
        if (superClazz != null) {
            copyTraceable(superClazz, source, target);
        }
    }


    @Override
    public void publish(TraceableValue value) {
        var data = SseEmitter.event().id(String.valueOf(value.getInstanceId())).name("update").data(value);
        for (var emitter : clients) {
            try {
                emitter.send(data);
            } catch (IOException e) {
                clients.remove(emitter);
            }
        }
    }
}
