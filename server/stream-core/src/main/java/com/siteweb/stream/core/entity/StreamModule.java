package com.siteweb.stream.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-05-28)
 **/
@Data
@TableName("tcs_stream_modules")
public class StreamModule {

    // 模块唯一ID
    @TableId(value = "module_id", type = IdType.AUTO)
    private String moduleId;

    // 模块名称
    @TableField(value = "module_name")
    private String moduleName;

    // 模块版本
    @TableField(value = "module_version")
    private String moduleVersion;

    // 模块支持
    @TableField(value = "module_provider")
    private String moduleProvider;

    // Jar包路径
    @TableField(value = "jar_file")
    private String jarFile;

    // Jar包code
    @TableField(value = "jar_code")
    private String jarCode;


    // 是否启用
    @TableField(value = "enable")
    private Boolean enable;


    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "updated_by")
    private String updatedBy;

}
