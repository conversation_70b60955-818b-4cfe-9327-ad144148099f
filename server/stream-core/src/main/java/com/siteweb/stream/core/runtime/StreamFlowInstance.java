package com.siteweb.stream.core.runtime;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.stream.common.messages.BusinessMessage;
import com.siteweb.stream.common.runtime.events.FlowOptionChangeEvent;
import com.siteweb.stream.common.runtime.events.FlowStartEvent;
import com.siteweb.stream.common.runtime.events.FlowTerminateEvent;
import com.siteweb.stream.common.stream.FlowRuntimeContext;
import com.siteweb.stream.common.stream.StreamFlowOption;
import com.siteweb.stream.common.util.Parameters;
import com.siteweb.stream.core.entity.StreamFlow;
import lombok.Data;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @ClassName: StreamFlowInstance
 * @descriptions:
 * @author: xsx
 * @date: 2/14/2025 6:11 PM
 **/
@Data
public class StreamFlowInstance {
    private StreamFlow streamFlowInfo;
    private ActorRef streamFlowActorRef;

    private Parameters streamFlowParameter;

    private FlowRuntimeContext flowRuntimeContext;

    //flow的actor是否已启动
    private Boolean actorStarted;

    private StreamFlowOption streamFlowOption;

    public void start(FlowStartEvent flowStartEvent) {
        //flowStartEvent继续包装
        streamFlowActorRef.tell(flowStartEvent, ActorRef.noSender());
    }

    public void stop(FlowTerminateEvent flowTerminateEvent) {
        //flowTerminateEvent继续包装
        streamFlowActorRef.tell(flowTerminateEvent, ActorRef.noSender());
    }

//    public void restart(){
//        stop();
//        start();
//    }

    //更新流配置参数
    public void updateFlowInstanceOption(FlowOptionChangeEvent streamFlowOptionChangeMessage) {
        this.streamFlowOption = streamFlowOptionChangeMessage.getStreamFlowOption();
        streamFlowActorRef.tell(streamFlowOptionChangeMessage, ActorRef.noSender());
    }

    public void notifyBusinessMessage(BusinessMessage businessMessage, ActorContext actorContext) {
        if(ObjectUtil.isNotEmpty(businessMessage.getStreamFlowId()) && businessMessage.getStreamFlowId().longValue() != streamFlowInfo.getStreamFlowId())
            return;
        streamFlowActorRef.forward(businessMessage, actorContext);
    }
}
