package com.siteweb.stream.core.runtime;

import com.siteweb.stream.common.runtime.events.NodeInstanceDeadLetter;
import com.siteweb.stream.common.stream.FlowRuntimeContext;
import com.siteweb.stream.common.stream.StreamLink;
import com.siteweb.stream.core.entity.StreamFlow;
import com.siteweb.stream.core.entity.StreamNode;
import com.siteweb.stream.core.manager.StreamModuleManager;
import org.apache.pekko.actor.ActorContext;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: RunnableGraphBuilder
 * @descriptions: actor模式下流模型-可运行图构造器 单例模式
 * @author: xsx
 * @date: 2/14/2025 12:53 PM
 **/
public class RunnableGraphBuilder {
    //获取ShapeFactory
    private final StreamModuleManager shapeFactory = StreamModuleManager.INSTANCE;
    // 提供全局访问点
    public static RunnableGraphBuilder getInstance() {
        return SingletonHolder.INSTANCE;
    }

    public StreamShapeInstance build(ActorContext actorContext, StreamFlow streamFlowInfo, Map<Long, StreamShapeInstance> streamShapeInstanceMap, FlowRuntimeContext flowRuntimeContext) throws Exception {
        List<StreamNode> streamNodeList = streamFlowInfo.getNodes();
        List<StreamLink> linkList = streamFlowInfo.getLinks();
        if (CollectionUtils.isEmpty(streamNodeList)) {
            throw new Exception("shape list is empty!");
        }
        try {
            //源节点
            long sourceNodeId = linkList.get(0).getInNodeId();
            //循环创建
            for (StreamNode node : streamNodeList) {


                // TODO 换成这个API 传入 GraphContext 和 FlowContext
                StreamShapeInstance shapeInstance = shapeFactory.createShapeInstance(node, actorContext, flowRuntimeContext.getGraphRuntimeContext(),flowRuntimeContext);
                streamShapeInstanceMap.put(node.getStreamNodeId(),shapeInstance);
//                shapeInstance.options(node.getOption());
                //做好遗言机制
                actorContext.watchWith(shapeInstance.getShapeActor(), new NodeInstanceDeadLetter(node.getStreamNodeId()));
            }
            //连线 link
            for (StreamLink streamLink : linkList) {
                // 找第一个节点id
                if (sourceNodeId == streamLink.getOutNodeId()) {
                    sourceNodeId = streamLink.getInNodeId();
                }
                // from
                long inStreamShapeNodeId = streamLink.getInNodeId();
                // to
                long outStreamShapeNodeId = streamLink.getOutNodeId();
                StreamShapeInstance currentNodeInstance = streamShapeInstanceMap.get(inStreamShapeNodeId);
                StreamShapeInstance nextNodeInstance = streamShapeInstanceMap.get(outStreamShapeNodeId);
                // todo 告诉当前节点下一个节点的actor ref
                currentNodeInstance.linkTo(streamLink.getOutletId(), nextNodeInstance);
            }
            //返回
            return streamShapeInstanceMap.get(sourceNodeId);

        } catch (Exception ex) {
            //异常要把已创建的actor杀死，不然堆内存 todo 需要验证
            if (!CollectionUtils.isEmpty(streamShapeInstanceMap)) {
                streamShapeInstanceMap.entrySet().stream()
                        .forEach(e -> e.getValue().stop());
            }
            throw new Exception(ex);
        }
    }

    // 静态内部类，只有在调用 getInstance 时才会加载
    private static class SingletonHolder {
        private static final RunnableGraphBuilder INSTANCE = new RunnableGraphBuilder();
    }

}
