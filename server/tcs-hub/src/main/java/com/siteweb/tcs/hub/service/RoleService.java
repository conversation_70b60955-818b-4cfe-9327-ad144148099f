package com.siteweb.tcs.hub.service;

import com.siteweb.tcs.hub.dal.entity.Role;

import java.util.List;

public interface RoleService {
    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 创建的角色信息
     */
    Role createRole(Role roleDTO);

    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 更新后的角色信息
     */
    Role updateRole(Role roleDTO);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean removeRole(Integer roleId);

    /**
     * 获取角色信息
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    Role getById(Integer roleId);

    /**
     * 获取所有角色
     *
     * @return 角色列表
     */
    List<Role> getAllRoles();

    /**
     * 获取用户的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getRolesByUserId(Integer userId);
}
