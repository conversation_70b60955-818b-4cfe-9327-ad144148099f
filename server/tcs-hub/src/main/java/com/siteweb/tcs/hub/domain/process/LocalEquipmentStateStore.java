package com.siteweb.tcs.hub.domain.process;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.domain.letter.EquipmentChange;
import com.siteweb.tcs.hub.domain.letter.GetEquipmentStateAction;
import com.siteweb.tcs.hub.domain.letter.LiveEquipmentState;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LocalEquipmentStateStore {
    private static final ConcurrentHashMap<Integer, LiveEquipmentState> equipmentStateMap = new ConcurrentHashMap<>();

    public static void saveEquipmentState(EquipmentChange equipmentChange)
    {
        equipmentStateMap.computeIfPresent(equipmentChange.getEquipmentId(), (key, value) -> {
            value.update(equipmentChange);
            return value;
        });
        equipmentStateMap.computeIfAbsent(equipmentChange.getEquipmentId(), (key) -> {
            LiveEquipmentState liveEquipmentState = new LiveEquipmentState();

            liveEquipmentState.setMonitorUnitId(equipmentChange.getMonitorUnitId());
            liveEquipmentState.setEquipmentId(equipmentChange.getEquipmentId());

            liveEquipmentState.update(equipmentChange);
            return liveEquipmentState;
        });
    }

    public static List<LiveEquipmentState> queryAllEquipmentState()
    {
        return equipmentStateMap.values().stream().toList();
    }

    public static List<LiveEquipmentState> queryEquipmentState(GetEquipmentStateAction action){
        if(action.isGetAllData()) return queryAllEquipmentState();
        if(CollectionUtil.isEmpty(action.getEquipmentIds())) return Collections.emptyList();
        List<LiveEquipmentState> liveEquipmentStates = equipmentStateMap.entrySet().stream()
                .filter(e -> action.getEquipmentIds().contains(e.getKey().intValue()))
                .map(Map.Entry::getValue).toList();
        return liveEquipmentStates;
    }

    public static LiveEquipmentState queryEquipmentState(Integer equipmentId){
        if(ObjectUtil.isEmpty(equipmentId) || !equipmentStateMap.containsKey(equipmentId)) return null;
        return equipmentStateMap.get(equipmentId);
    }

    public static boolean deleteEquipmentState(Integer equipmentId){
        if(ObjectUtil.isEmpty(equipmentId) || !equipmentStateMap.containsKey(equipmentId)) return true;
        equipmentStateMap.remove(equipmentId);
        return true;
    }
}
