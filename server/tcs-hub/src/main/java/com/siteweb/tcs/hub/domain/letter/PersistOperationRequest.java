package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.PersistOperationType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class PersistOperationRequest implements Serializable {

    private final PersistOperationType persistOperationType;
    private final LifeCycleEvent event;
    private final long deliveryId;

    public PersistOperationRequest(PersistOperationType persistOperationType, LifeCycleEvent event, long deliveryId) {
        this.persistOperationType = persistOperationType;
        this.event = event;
        this.deliveryId = deliveryId;
    }


}
