package com.siteweb.tcs.hub.domain.letter.enums;

/**
 * <AUTHOR> (2024-08-09)
 **/
public enum DynamicValueType {
    /// <summary>
    /// 空的
    /// </summary>
    Empty(-1),
    /// <summary>
    /// 浮点数
    /// </summary>
    Float(0),

    /// <summary>
    /// 字符串
    /// </summary>
    String(1),

    /// <summary>
    /// 整型
    /// </summary>
    Integer(2),

    /// <summary>
    /// 字节
    /// </summary>
    Byte(3),

    /// <summary>
    /// 字符
    /// </summary>
    Char(4),

    /// <summary>
    /// 网络时间
    /// </summary>
    NTPTime(5),

    /// <summary>
    /// 二进制
    /// </summary>
    Binary(6),

    /// <summary>
    /// 对象
    /// </summary>
    Object(7),

    /// <summary>
    /// JPEG图片
    /// </summary>
    JPEG(10),
    /// <summary>
    /// 指纹
    /// </summary>
    DOOR(99);

    private final int _value;

    DynamicValueType(int value) {
        this._value = value;
    }

    public int value() {
        return this._value;
    }
    public static DynamicValueType fromInt(int i) {
        for (DynamicValueType status : DynamicValueType.values()) {
            if (status._value == i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }

}
