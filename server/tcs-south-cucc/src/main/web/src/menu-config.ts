import type { RouteRecordRaw } from "vue-router";

import Home from "@/views/welcome/index.vue";

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: "ic:baseline-extension",
      keepAlive: true,
      order: 10002,
      title: "中国联通"
    },
    name: "tcs-south-cucc",
    path: "/tcs-south-cucc",
    children: [
      {
        meta: {
          requiresMenuAuth: true,
          title: "联通首页",
          keepAlive: true
        },
        name: "home",
        path: "home",
        component: Home
      }
    ]
  }
];

export default routes;
