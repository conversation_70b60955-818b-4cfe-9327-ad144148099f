package com.siteweb.stream.defaults.basics;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.options.defaults.CommentDefaultOption;
import lombok.extern.slf4j.Slf4j;

/**
 * 注释组件，提供文本注释功能
 *
 * <AUTHOR> (2025-02-20)
 **/

@Slf4j
@EditorHidden
@Shape(type = "comment")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor="#FFFFFF")
@ShapeDefaultOptions(CommentDefaultOption.class)
public class CommentShape extends AbstractShape {

    public CommentShape(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {

    }

}
