<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.siteweb</groupId>
        <artifactId>thing-connect-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>stream-south-cmcc</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <stream.module.id>stream-cmcc-libs</stream.module.id>
        <stream.module.name>中国移动流处理插件</stream.module.name>
        <stream.module.version>1.0.0</stream.module.version>
        <stream.module.provider>Siteweb</stream.module.provider>
        <stream.module.applicationName>中国移动流处理插件</stream.module.applicationName>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>stream-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>stream-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tsc-cmcc-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
<build>
    <plugins>
        <!-- 写入插件信息 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <configuration>
                <archive>
                    <manifestEntries>
                        <Plugin-Id>${stream.module.id}</Plugin-Id>
                        <Plugin-Name>${stream.module.name}</Plugin-Name>
                        <Plugin-Version>${stream.module.version}</Plugin-Version>
                        <Plugin-Provider>${stream.module.provider}</Plugin-Provider>
                        <Plugin-BuildTime>${maven.build.timestamp}</Plugin-BuildTime>
                    </manifestEntries>
                </archive>
            </configuration>
        </plugin>
    </plugins>

</build>

</project>