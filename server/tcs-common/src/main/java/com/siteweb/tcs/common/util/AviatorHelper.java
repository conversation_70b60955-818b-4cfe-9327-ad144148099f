package com.siteweb.tcs.common.util;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.siteweb.tcs.common.util.scripts.EmptyFunction;
import com.siteweb.tcs.common.util.scripts.JsonFunction;
import com.siteweb.tcs.common.util.scripts.SizeFunction;

/**
 * <AUTHOR> (2025-02-27)
 **/
public class AviatorHelper {

    public final static AviatorEvaluatorInstance DEFAULT = getDefault();

    private static AviatorEvaluatorInstance getDefault() {
        var instance = AviatorEvaluator.newInstance();
        instance.addFunction(new EmptyFunction());
        instance.addFunction(new SizeFunction());
        instance.addFunction(new JsonFunction());
        return instance;
    }


    public static Expression compile(String expression) {
        return getDefault().compile(expression);
    }


}
