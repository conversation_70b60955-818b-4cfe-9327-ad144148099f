package com.siteweb.tcs.common.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * API 操作结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功的响应结果
     */
    public static <T> ResponseResult<T> success(T data) {
        ResponseResult<T> result = new ResponseResult<>();
        result.setState(true);
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功的响应结果
     */
    public static <T> ResponseResult<T> success() {
        return success(null);
    }

    /**
     * 创建失败响应
     *
     * @param errMsg 错误消息
     * @param <T>    数据类型
     * @return 失败的响应结果
     */
    public static <T> ResponseResult<T> fail(String errMsg) {
        ResponseResult<T> result = new ResponseResult<>();
        result.setState(false);
        result.setErrMsg(errMsg);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    /**
     * 创建失败响应
     *
     * @param errMsg  错误消息
     * @param errCode 错误码
     * @param <T>     数据类型
     * @return 失败的响应结果
     */
    public static <T> ResponseResult<T> fail(String errMsg, String errCode) {
        ResponseResult<T> result = new ResponseResult<>();
        result.setState(false);
        result.setErrMsg(errMsg);
        result.setErrCode(errCode);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    /**
     * 执行结果
     */
    private boolean state;
    /**
     * 错误消息
     */
    @JsonProperty("err_msg")
    private String errMsg;

    /**
     * 错误码
     */
    @JsonProperty("err_code")
    private String errCode;

    /**
     * 执行时间
     */
    private long timestamp;

    /**
     * 返回内容
     */
    private T data;
}
