package com.siteweb.tcs.common.db.dialect;

/**
 * 数据库方言接口
 * 定义所有数据库方言需要实现的方法
 */
public interface DatabaseDialectInterface {
    
    /**
     * 获取分页SQL
     *
     * @param sql    原始SQL
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 带分页的SQL
     */
    String getPaginationSql(String sql, int offset, int limit);
    
    /**
     * 获取自增列SQL
     *
     * @param columnName 列名
     * @return 自增列SQL
     */
    String getAutoIncrementSql(String columnName);
    
    /**
     * 获取当前时间函数
     *
     * @return 当前时间函数
     */
    String getCurrentTimestampFunction();
    
    /**
     * 获取字符串连接函数
     *
     * @param strings 要连接的字符串
     * @return 连接后的字符串
     */
    String getConcatFunction(String... strings);
    
    /**
     * 获取LIMIT语句
     *
     * @param limit 限制数量
     * @return LIMIT语句
     */
    String getLimitSql(int limit);
    
    /**
     * 获取适用于当前数据库的建表语句中的引擎和字符集部分
     *
     * @return 引擎和字符集SQL片段
     */
    String getTableOptions();
    
    /**
     * 转换存储过程
     * 将标准格式的存储过程转换为当前数据库支持的格式
     *
     * @param procedureScript 存储过程脚本
     * @return 转换后的存储过程脚本
     */
    String convertStoredProcedure(String procedureScript);
    
    /**
     * 检查数据库是否支持存储过程
     *
     * @return 是否支持存储过程
     */
    boolean supportsStoredProcedures();
}