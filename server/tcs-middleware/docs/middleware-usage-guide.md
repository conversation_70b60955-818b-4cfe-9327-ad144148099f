# TCS 中间件资源与服务使用指南

本文档提供了关于如何在TCS项目中使用ResourceRegistry和ServiceRegistry的详细指导，包括各种Resource和Service的特性方法和配置模板。

## 目录

1. [基本使用流程](#1-基本使用流程)
2. [在插件中配置资源和服务](#2-在插件中配置资源和服务)
3. [使用ResourceRegistry和ServiceRegistry](#3-使用resourceregistry和serviceregistry)
4. [各种Resource的特性和原生对象](#4-各种resource的特性和原生对象)
5. [各种Service的特性和功能](#5-各种service的特性和功能)
6. [注意事项](#6-注意事项)
7. [附录：配置模板](#7-附录配置模板)

## 1. 基本使用流程

使用TCS中间件资源和服务的基本流程如下：

1. 在插件的`pom.xml`中添加对`tcs-middleware-common`的依赖
2. 在插件的`plugin.yml`中配置需要使用的资源和服务ID
3. 在代码中通过注解或编程方式获取资源和服务
4. 使用资源和服务提供的方法完成业务逻辑

## 2. 在插件中配置资源和服务

### 2.1 添加依赖

在插件的`pom.xml`中添加以下依赖：

```xml
<dependency>
    <groupId>com.siteweb.tcs</groupId>
    <artifactId>tcs-middleware-common</artifactId>
    <version>${tcs.version}</version>
</dependency>
```

### 2.2 配置plugin.yml

在插件的`plugin.yml`中配置需要使用的资源和服务ID，参考`tcs-south-cucc`的配置方式：

```yaml
plugin:
  id: your-plugin-id
  name: 你的插件名称
  version: 1.0.0
  provider: Siteweb

  # 中间件资源配置
  middleware:
    database:
      primary: your-db-resource-id  # 主数据库资源ID
    redis:
      primary: your-redis-resource-id  # 主Redis资源ID
    kafka:
      primary: your-kafka-resource-id  # 主Kafka资源ID
    mqtt:
      primary: your-mqtt-resource-id  # 主MQTT资源ID
```

这种配置方式的主要作用是可以通过`@Value`注解来获取资源ID，避免在代码中硬编码资源ID。

## 3. 使用ResourceRegistry和ServiceRegistry

### 3.1 通过注解注入

在主项目Spring管理的Bean中，可以使用`@MwResource`和`@MwService`注解来注入资源和服务，依赖于上下文中的注解处理器：

```java
@Component
public class MyComponent {

    @MwResource("my-db-resource")
    private MySQLResource mySQLResource;

    @MwService("my-db-service")
    private DatabaseService databaseService;

    @MwResource("my-redis-resource")
    private RedisResource redisResource;

    @MwService("my-kv-service")
    private KeyValueStoreService keyValueStoreService;

    public void doSomething() {
        // 使用资源和服务
        try {
            List<Map<String, Object>> results = databaseService.query("SELECT * FROM users");
            keyValueStoreService.set("user-count", String.valueOf(results.size()));
        } catch (Exception e) {
            // 处理异常
        }
    }
}
```

### 3.2 通过编程方式获取

在各种情形下，可以通过编程方式获取资源和服务：

```java
@Autowired
private ResourceRegistry resourceRegistry;

@Autowired
private ServiceRegistry serviceRegistry;

@Value("${plugin.middleware.database.primary}")
private String dbResourceId;

public void useResource() {
    // 获取资源 your-plugin-id 用于标识引用
    Resource resource = resourceRegistry.get(dbResourceId, "your-plugin-id");
    if (resource instanceof MySQLResource) {
        MySQLResource mySQLResource = (MySQLResource) resource;
        DataSource dataSource = mySQLResource.getDataSource();
        // 使用数据源...
    }

    // 获取服务
    Service service = serviceRegistry.get("my-db-service");
    if (service instanceof DatabaseService) {
        DatabaseService databaseService = (DatabaseService) service;
        try {
            List<Map<String, Object>> results = databaseService.query("SELECT * FROM users");
            // 处理查询结果...
        } catch (SQLException e) {
            // 处理异常
        }
    }
}
```

### 3.3 获取原生对象

ResourceRegistry提供了一些便捷方法来直接获取原生对象：

```java
// 获取DataSource
DataSource dataSource = resourceRegistry.getDataSource("my-db-resource", "your-plugin-id");

// 获取RedisTemplate
RedisTemplate<String, String> redisTemplate = resourceRegistry.getRedisTemplate("my-redis-resource", "your-plugin-id");

// 获取KafkaTemplate
KafkaTemplate<String, String> kafkaTemplate = resourceRegistry.getKafkaTemplate("my-kafka-resource", "your-plugin-id");
```

## 4. 各种Resource的特性和原生对象

### 4.1 MySQLResource

**原生对象类型**：`javax.sql.DataSource`

**主要方法**：
- `DataSource getDataSource()`：获取数据源
- `Connection getConnection()`：获取数据库连接
- `<T> T getNativeResource()`：获取原生资源对象（DataSource）

**使用示例**：
```java
MySQLResource mySQLResource = (MySQLResource) resourceRegistry.get("my-mysql-resource", "your-plugin-id");
DataSource dataSource = mySQLResource.getDataSource();
try (Connection conn = dataSource.getConnection();
     PreparedStatement stmt = conn.prepareStatement("SELECT * FROM users")) {
    ResultSet rs = stmt.executeQuery();
    // 处理结果集...
}
```

### 4.2 H2Resource

**原生对象类型**：`javax.sql.DataSource`

**主要方法**：
- `DataSource getDataSource()`：获取数据源
- `Connection getConnection()`：获取数据库连接
- `<T> T getNativeResource()`：获取原生资源对象（DataSource）

**使用示例**：
```java
// 直接从ResourceRegistry获取DataSource
DataSource dataSource = resourceRegistry.getDataSource("my-h2-resource", "your-plugin-id");
try (Connection conn = dataSource.getConnection()) {
    // 使用连接...
}
```

### 4.3 RedisResource

**原生对象类型**：`org.springframework.data.redis.core.RedisTemplate<String, Object>`

**主要方法**：
- `RedisTemplate<String, Object> getRedisTemplate()`：获取Redis模板
- `RedisConnectionFactory getConnectionFactory()`：获取Redis连接工厂
- `<T> T getNativeResource()`：获取原生资源对象（RedisTemplate）
- 以及get、set等简单封装RedisTemplate的方法

**使用示例**：
```java
RedisResource redisResource = (RedisResource) resourceRegistry.get("my-redis-resource", "your-plugin-id");
RedisTemplate<String, Object> redisTemplate = redisResource.getRedisTemplate();
redisTemplate.opsForValue().set("key", "value");
Object valueObj = redisTemplate.opsForValue().get("key");
String value = valueObj != null ? valueObj.toString() : null;
```

### 4.4 KafkaResource

**原生对象类型**：`org.springframework.kafka.core.KafkaTemplate<String, String>`

**主要方法**：
- `KafkaTemplate<String, String> getKafkaTemplate()`：获取Kafka模板
- `ProducerFactory<String, String> getProducerFactory()`：获取生产者工厂
- `<T> T getNativeResource()`：获取原生资源对象（KafkaTemplate）

**使用示例**：
```java
KafkaResource kafkaResource = (KafkaResource) resourceRegistry.get("my-kafka-resource", "your-plugin-id");
KafkaTemplate<String, String> kafkaTemplate = kafkaResource.getKafkaTemplate();
kafkaTemplate.send("topic-name", "message-content");
```

### 4.5 MosMQTTResource

**原生对象类型**：`org.eclipse.paho.client.mqttv3.MqttClient`

**主要方法**：
- `MqttClient getMqttClient()`：获取MQTT客户端
- `void publish(String topic, String message, int qos, boolean retained)`：发布消息
- `void subscribe(String topic, int qos, IMqttMessageListener listener)`：订阅主题
- `<T> T getNativeResource()`：获取原生资源对象（MqttClient）

**使用示例**：
```java
MosMQTTResource mqttResource = (MosMQTTResource) resourceRegistry.get("my-mqtt-resource", "your-plugin-id");
MqttClient mqttClient = mqttResource.getMqttClient();
mqttClient.publish("my/topic", "Hello MQTT", 1, false);
mqttClient.subscribe("my/topic", 1, (topic, message) -> {
    System.out.println("Received: " + new String(message.getPayload()));
});
```

### 4.6 HttpServerResource

**原生对象类型**：`org.apache.pekko.http.javadsl.ServerBinding`

**主要方法**：
- `ServerBinding getServerBinding()`：获取服务器绑定
- `void bindRoute(Route route)`：绑定路由
- `<T> T getNativeResource()`：获取原生资源对象（ServerBinding）

**使用示例**：
```java
HttpServerResource httpResource = (HttpServerResource) resourceRegistry.get("my-http-resource", "your-plugin-id");
Route route = get(() ->
    path("hello", () ->
        complete("Hello, World!")
    )
);
httpResource.bindRoute(route);
```

## 5. 各种Service的特性和功能

### 5.1 DatabaseService

**关联资源类型**：`MySQLResource`、`H2Resource`等数据库资源

**主要方法**：
- `Connection getConnection()`：获取数据库连接
- `List<Map<String, Object>> query(String sql, Object... params)`：执行查询SQL，返回结果列表
- `int update(String sql, Object... params)`：执行更新SQL，返回影响行数
- `<T> T queryForObject(String sql, Class<T> requiredType, Object... params)`：执行查询SQL，返回单个值

**使用示例**：
```java
@MwService("my-db-service")
private DatabaseService databaseService;

public void useService() {
    try {
        // 执行查询
        List<Map<String, Object>> users = databaseService.query("SELECT * FROM users WHERE age > ?", 18);

        // 执行更新
        int affected = databaseService.update("UPDATE users SET status = ? WHERE id = ?", "active", 1);

        // 查询单个值
        Long count = databaseService.queryForObject("SELECT COUNT(*) FROM users", Long.class);
    } catch (SQLException e) {
        // 处理异常
    }
}
```

### 5.2 KeyValueStoreService

**关联资源类型**：`RedisResource`

**主要方法**：
- `void set(String key, String value)`：设置键值
- `String get(String key)`：获取值
- `void delete(String key)`：删除键值
- `void setWithExpiry(String key, String value, long seconds)`：设置带过期时间的键值
- `void multiSet(Map<String, String> map)`：批量设置键值
- `Map<String, String> multiGet(Collection<String> keys)`：批量获取键值
- `Long increment(String key)`：原子递增
- `Long increment(String key, long delta)`：原子递增指定值

**使用示例**：
```java
@MwService("my-kv-service")
private KeyValueStoreService keyValueStoreService;

public void useService() {
    // 设置值
    keyValueStoreService.set("key", "value");

    // 获取值
    String value = keyValueStoreService.get("key");

    // 删除值
    keyValueStoreService.delete("key");

    // 批量操作
    keyValueStoreService.multiSet(Map.of(
        "key1", "value1",
        "key2", "value2",
        "key3", "value3"
    ));

    // 设置过期时间
    keyValueStoreService.setWithExpiry("key", "value", 60);

    // 原子操作
    keyValueStoreService.increment("counter");
    keyValueStoreService.increment("counter", 5);
}
```

## 6. 注意事项

在使用ResourceRegistry和ServiceRegistry时，需要注意以下几点：

1. **资源和服务的生命周期**：资源和服务都有自己的生命周期（初始化、启动、停止、销毁），通常不需要手动管理，系统会自动处理。

2. **插件中的注解注入限制**：在插件中（无论是主类还是内部组件），`@MwResource` 和 `@MwService` 注解通常无法正常工作，因为注解处理器不会自动扩展到插件的 Spring 上下文中。在插件中，应该通过编程方式使用 `ResourceRegistry` 和 `ServiceRegistry` 获取资源和服务。

3. **强制使用引用计数的资源获取方式**：
   - **必须使用带referenceId的方法**：所有资源获取都必须使用 `resourceRegistry.get(resourceId, referenceId)` 或 `resourceRegistry.getDataSource(resourceId, referenceId)` 等带引用者ID的方法。
   - **禁止使用无引用计数的方法**：不要使用 `resourceRegistry.get(resourceId)` 或 `resourceRegistry.getDataSource(resourceId)` 等不带引用者ID的方法，这些方法不会进行引用计数，可能导致资源生命周期管理问题。
   - **referenceId通常是插件ID**：在插件中获取资源时，referenceId应该传入插件的ID，确保资源引用计数的正确性。

4. **服务获取也需要引用计数**：
   - 获取服务时也应该使用 `serviceRegistry.get(serviceId, referenceId)` 方法，传入引用者ID。
   - 服务在创建时会自动以serviceId作为referenceId引用其依赖的资源。

5. **资源和服务的延迟加载**：资源和服务是延迟加载的，只有在第一次使用时才会创建实例。

6. **资源和服务的配置**：资源和服务的配置是在中间件管理界面中进行的，不需要在代码中配置。

7. **资源和服务的异常处理**：使用资源和服务时，需要处理可能抛出的异常，如`ResourceException`和`ServiceException`，他们继承自 TCSException。

8. **批处理优化**：KeyValueStoreService在封装RedisResource的基础上，优化批处理操作，当批处理队列达到阈值（默认100）或超过时间阈值（默认3秒）时，会自动执行批处理。

9. **插件生命周期管理**：
   - 插件启动时，系统会自动清理该插件之前可能残留的引用。
   - 插件停止时，系统会自动清理该插件对所有服务和资源的引用。
   - 引用计数为0的资源和服务会被自动销毁。

## 6.1 引用计数机制详解

### 6.1.1 引用计数的工作原理

系统采用了改进的引用计数机制来管理资源和服务的生命周期：

- **详细引用计数**：记录每个引用者（通常是插件ID）对每个资源/服务的具体引用次数
- **总引用计数**：记录资源/服务的总引用次数，用于快速判断是否可以销毁
- **引用者集合**：记录所有引用者的ID，用于快速查询和批量操作

### 6.1.2 引用计数的操作类型

1. **单次引用移除**：`removeResourceReference(resourceId, referenceId)` - 只移除一次引用
2. **全部引用移除**：`removeResourceReference(resourceId, referenceId, true)` - 移除该引用者的所有引用
3. **批量引用移除**：`batchRemoveResourceReferences(referenceId)` - 移除该引用者对所有资源的引用

### 6.1.3 插件生命周期集成

插件停止时会自动调用 `serviceRegistry.cleanupPluginReferences(pluginId)`，该方法会：
1. 先清理插件对所有服务的引用
2. 再清理插件对所有资源的引用
3. 确保资源和服务的正确销毁顺序

## 7. 附录：配置模板

以下配置模板仅包含各资源和服务在创建过程中实际使用到的必要参数，便于开发者快速配置和使用。

### 7.1 资源配置模板

#### 7.1.1 MySQLResource配置模板

```json
{
    "host": "localhost",
    "port": 3306,
    "database": "tcs_middleware",
    "username": "root",
    "password": "password",
    "minIdle": 5,
    "maxPoolSize": 20,
    "connectionTimeout": 30000
}
```

#### 7.1.2 H2Resource配置模板

```json
{
    "dbName": "testdb",
    "mode": "MEMORY",  // 可选值: MEMORY, FILE, TCP
    "filePath": "./h2db/testdb",  // 仅FILE模式需要
    "host": "localhost",  // 仅TCP模式需要
    "port": 9092,
    "username": "sa",
    "password": "",
    "maxPoolSize": 10,
    "minIdle": 1
}
```

#### 7.1.3 RedisResource配置模板

```json
{
    "host": "localhost",
    "port": 6379,
    "password": "",
    "database": 0,
    "connectionTimeout": 2000,
    "maxTotal": 8,
    "maxIdle": 8,
    "minIdle": 0
}
```

#### 7.1.4 KafkaResource配置模板

```json
{
    "bootstrapServers": "localhost:9092",
    "clientId": "tcs-middleware",
    "keySerializer": "org.apache.kafka.common.serialization.StringSerializer",
    "valueSerializer": "org.apache.kafka.common.serialization.StringSerializer",
    "acks": "all",
    "retries": 3,
    "batchSize": 16384,
    "lingerMs": 1,
    "bufferMemory": 33554432
}
```

#### 7.1.5 MosMQTTResource配置模板

```json
{
    "serverUri": "tcp://localhost:1883",
    "clientId": "tcs-middleware",
    "username": "",
    "password": "",
    "cleanSession": true,
    "connectionTimeout": 30,
    "keepAliveInterval": 60,
    "automaticReconnect": true
}
```

#### 7.1.6 HttpServerResource配置模板

```json
{
    "host": "0.0.0.0",
    "port": 8080,
    "idleTimeout": 60,
    "backlog": 100
}
```

### 7.2 服务配置模板

#### 7.2.1 DatabaseService配置模板

DatabaseService不需要额外配置，它直接使用关联的数据库资源的配置。

```json
{}
```

#### 7.2.2 KeyValueStoreService配置模板

```json
{
    "batchThreshold": 100,
    "timeThresholdMs": 3000
}
```

### 7.3 在plugin.yml中的配置示例

以下是在插件的`plugin.yml`中配置资源和服务ID的示例：

```yaml
plugin:
  id: your-plugin-id
  name: 你的插件名称
  version: 1.0.0
  provider: Siteweb

  # 中间件资源配置
  middleware:
    # 数据库资源
    database:
      primary: your-db-resource-id  # 主数据库资源ID

    # Redis资源
    redis:
      primary: your-redis-resource-id  # 主Redis资源ID

    # Kafka资源
    kafka:
      primary: your-kafka-resource-id  # 主Kafka资源ID

    # MQTT资源
    mqtt:
      primary: your-mqtt-resource-id  # 主MQTT资源ID
```
