package com.siteweb.tcs.middleware.controller.crud;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.i18n.NamespacedMessageSource;
import com.siteweb.tcs.middleware.entity.ServiceConfigurationEntity;
import com.siteweb.tcs.middleware.service.ServiceConfigurationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务配置控制器
 * 提供服务配置的CRUD接口
 */
@RestController
@RequestMapping("/middleware/service-configs")
@Tag(name = "服务配置管理", description = "服务配置的CRUD接口")
public class ServiceConfigController {

    @Autowired
    private ServiceConfigurationService serviceConfigurationService;

    @Autowired
    @Qualifier("middlewareMessageSource")
    private NamespacedMessageSource messageSource;

    /**
     * 分页查询服务配置
     *
     * @param current 当前页
     * @param size 每页大小
     * @param name 服务配置名称（可选，用于模糊查询）
     * @param serviceId 服务类型ID（可选，用于筛选）
     * @param status 服务配置状态（可选，用于筛选）
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询服务配置", description = "分页查询服务配置，支持按名称模糊查询和服务类型筛选")
    public ResponseResult<IPage<ServiceConfigurationEntity>> page(
            @Parameter(description = "当前页码", required = true) @RequestParam(defaultValue = "1") long current,
            @Parameter(description = "每页大小", required = true) @RequestParam(defaultValue = "10") long size,
            @Parameter(description = "服务配置名称（可选）") @RequestParam(required = false) String name,
            @Parameter(description = "服务类型ID（可选）") @RequestParam(required = false) String serviceId,
            @Parameter(description = "服务配置状态（可选）") @RequestParam(required = false) String status) {

        Page<ServiceConfigurationEntity> page = new Page<>(current, size);
        IPage<ServiceConfigurationEntity> result = serviceConfigurationService.pageServiceConfigurations(page, serviceId, name, status);

        return ResponseResult.success(result);
    }

    /**
     * 获取所有服务配置
     *
     * @return 所有服务配置列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取所有服务配置", description = "获取所有服务配置列表")
    public ResponseResult<List<ServiceConfigurationEntity>> list() {
        List<ServiceConfigurationEntity> list = serviceConfigurationService.list();
        return ResponseResult.success(list);
    }

    /**
     * 根据ID获取服务配置
     *
     * @param id 服务配置ID
     * @return 服务配置详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取服务配置", description = "根据ID获取服务配置详情")
    public ResponseResult<ServiceConfigurationEntity> getById(@PathVariable String id) {
        ServiceConfigurationEntity serviceConfig = serviceConfigurationService.getServiceConfigurationById(id);
        return ResponseResult.success(serviceConfig);
    }

    /**
     * 根据服务类型ID获取服务配置列表
     *
     * @param serviceId 服务类型ID
     * @return 服务配置列表
     */
    @GetMapping("/service-type/{serviceId}")
    @Operation(summary = "根据服务类型获取服务配置", description = "根据服务类型ID获取服务配置列表")
    public ResponseResult<List<ServiceConfigurationEntity>> getByServiceId(@PathVariable String serviceId) {
        List<ServiceConfigurationEntity> list = serviceConfigurationService.listServiceConfigurationsByServiceId(serviceId);
        return ResponseResult.success(list);
    }

    /**
     * 根据资源配置ID获取服务配置列表
     *
     * @param resourceConfigId 资源配置ID
     * @return 服务配置列表
     */
    @GetMapping("/resource-config/{resourceConfigId}")
    @Operation(summary = "根据资源配置获取服务配置", description = "根据资源配置ID获取服务配置列表")
    public ResponseResult<List<ServiceConfigurationEntity>> getByResourceConfigId(@PathVariable String resourceConfigId) {
        List<ServiceConfigurationEntity> list = serviceConfigurationService.listServiceConfigurationsByResourceConfigurationId(resourceConfigId);
        return ResponseResult.success(list);
    }

    /**
     * 创建服务配置
     *
     * @param serviceConfig 服务配置实体
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建服务配置", description = "创建新的服务配置")
    public ResponseResult<ServiceConfigurationEntity> create(@RequestBody ServiceConfigurationEntity serviceConfig) {
        ServiceConfigurationEntity created = serviceConfigurationService.createServiceConfiguration(serviceConfig);
        if (created != null) {
            return ResponseResult.success(created);
        } else {
            return ResponseResult.fail(messageSource.getMessage("service.config.create.failed"));
        }
    }

    /**
     * 更新服务配置
     *
     * @param id 服务配置ID
     * @param serviceConfig 服务配置实体
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新服务配置", description = "更新指定ID的服务配置")
    public ResponseResult<ServiceConfigurationEntity> update(@PathVariable String id, @RequestBody ServiceConfigurationEntity serviceConfig) {
        serviceConfig.setId(id);
        ServiceConfigurationEntity updated = serviceConfigurationService.updateServiceConfiguration(id, serviceConfig);
        if (updated != null) {
            return ResponseResult.success(updated);
        } else {
            return ResponseResult.fail(messageSource.getMessage("service.config.update.failed"));
        }
    }

    /**
     * 删除服务配置
     *
     * @param id 服务配置ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除服务配置", description = "删除指定ID的服务配置")
    public ResponseResult<Boolean> delete(@PathVariable String id) {
        boolean success = serviceConfigurationService.deleteServiceConfiguration(id);
        if (success) {
            return ResponseResult.success(true);
        } else {
            return ResponseResult.fail(messageSource.getMessage("service.config.delete.failed"));
        }
    }

    /**
     * 启用服务配置
     *
     * @param id 服务配置ID
     * @return 启用结果
     */
    @PostMapping("/enable/{id}")
    @Operation(summary = "启用服务配置", description = "启用指定服务配置")
    public ResponseResult<Boolean> enable(@PathVariable String id) {
        ServiceConfigurationEntity entity = serviceConfigurationService.getServiceConfigurationById(id);
        if (entity == null) {
            return ResponseResult.fail(messageSource.getMessage("service.config.not_found"));
        }

        entity.setStatus("ENABLED");
        ServiceConfigurationEntity updated = serviceConfigurationService.updateServiceConfiguration(id, entity);
        boolean success = updated != null;

        if (success) {
            return ResponseResult.success(true);
        } else {
            return ResponseResult.fail(messageSource.getMessage("service.config.update.failed"));
        }
    }

    /**
     * 禁用服务配置
     *
     * @param id 服务配置ID
     * @return 禁用结果
     */
    @PostMapping("/disable/{id}")
    @Operation(summary = "禁用服务配置", description = "禁用指定服务配置")
    public ResponseResult<Boolean> disable(@PathVariable String id) {
        ServiceConfigurationEntity entity = serviceConfigurationService.getServiceConfigurationById(id);
        if (entity == null) {
            return ResponseResult.fail(messageSource.getMessage("service.config.not_found"));
        }

        entity.setStatus("DISABLED");
        ServiceConfigurationEntity updated = serviceConfigurationService.updateServiceConfiguration(id, entity);
        boolean success = updated != null;

        if (success) {
            return ResponseResult.success(true);
        } else {
            return ResponseResult.fail(messageSource.getMessage("service.config.update.failed"));
        }
    }
}
