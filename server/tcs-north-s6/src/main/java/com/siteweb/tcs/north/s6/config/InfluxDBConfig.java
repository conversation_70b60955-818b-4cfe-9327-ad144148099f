package com.siteweb.tcs.north.s6.config;

/**
 * <AUTHOR> (2024-08-28)
 **/
import lombok.Data;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;


@Data
@Configuration
public class InfluxDBConfig {

    @Value("${siteweb.history-data.influxdb.url}")
    private String influxDbUrl;

    @Value("${siteweb.history-data.influxdb.username}")
    private String influxDbUsername;

    @Value("${siteweb.history-data.influxdb.password}")
    private String influxDbPassword;

    @Value("${siteweb.history-data.influxdb.database}")
    private String defaultDatabase;


    @Value("${siteweb.history-data.influxdb.measurement}")
    private String defaultMeasurement;





    @Bean
    public InfluxDB influxDB() {
        InfluxDB influxDB =  InfluxDBFactory.connect(influxDbUrl, influxDbUsername, influxDbPassword);
        influxDB.enableGzip();
        influxDB.setDatabase(defaultDatabase);
        // 每20W条或每5秒 批量写入
        influxDB.enableBatch(200000, 5000, TimeUnit.MILLISECONDS);
        return influxDB;
    }

}
