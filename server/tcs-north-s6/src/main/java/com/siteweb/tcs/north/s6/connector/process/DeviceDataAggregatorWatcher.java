package com.siteweb.tcs.north.s6.connector.process;

import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.hub.domain.letter.EquipmentRealSignal;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 设备数据聚合监视器
 * 用于订阅所有网关的所有设备的实时数据，并进行汇总统计
 * 演示如何使用GatewayPipelineEntry的分布式发布/订阅功能
 */
@Slf4j
public class DeviceDataAggregatorWatcher extends ProbeActor {

    private final String AGGREGATOR_LOG = "data_aggregator";

    // 用于统计每个设备的数据点数量
    private final Map<String, AtomicLong> deviceDataPointsCount = new HashMap<>();

    // 总数据点数量
    private final AtomicLong totalDataPoints = new AtomicLong(0);

    // 最后一次统计时间
    private long lastStatisticsTime = System.currentTimeMillis();

    // 统计间隔（毫秒）
    private static final long STATISTICS_INTERVAL = 60000; // 1分钟

    public DeviceDataAggregatorWatcher() {
        getProbe().addWindowLog(AGGREGATOR_LOG);
        getProbe().addCounter("TotalDataPointsCounter");
        getProbe().addRateCalculator("DataPointsRate", 60);

        // 订阅所有网关的实时数据
        subscribeToAllGateways();
    }

    public static Props props() {
        return Props.create(DeviceDataAggregatorWatcher.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentRealSignal.class, this::processRealSignal)
                .build()
                .orElse(super.createReceive());
    }

    /**
     * 订阅所有网关的实时数据
     * 通过向每个GatewayPipelineEntry发送订阅请求来实现
     */
    private void subscribeToAllGateways() {
        try {
            // 获取分布式发布/订阅中介器
            ActorRef mediator = DistributedPubSub.get(getContext().getSystem()).mediator();

            // 创建订阅请求
            SubscriptionRequest request = new SubscriptionRequest()
                    .setSubscriber(getSelf())
                    .setSubscriptionType(SubscriptionRequest.SubscriptionType.REAL_DATA);

            // 向中介器发送订阅消息，订阅"gateway-real-data"主题
            mediator.tell(
                new DistributedPubSubMediator.Subscribe("gateway-real-data", getSelf()),
                getSelf()
            );

            // 向所有GatewayPipelineEntry广播订阅请求
            mediator.tell(
                new DistributedPubSubMediator.Publish("gateway-pipeline-commands", request),
                getSelf()
            );

            getProbe().info("Subscribed to real-time data from all gateways using distributed pub/sub");
            getProbe().enqueueWindowLogItem(AGGREGATOR_LOG, new ActorLogItem(ActorLogLevel.INFO,
                    "已通过分布式发布/订阅机制订阅所有网关的实时数据"));
        } catch (Exception e) {
            LogUtil.error(log, "aggregator.subscribe.failed", e);
            getProbe().error("Failed to subscribe to gateways: " + e.getMessage());
        }
    }

    /**
     * 处理实时信号数据
     * 统计数据点数量并定期输出统计信息
     */
    private void processRealSignal(EquipmentRealSignal signal) {

    }

    /**
     * 输出统计信息
     */
    private void outputStatistics() {
        StringBuilder sb = new StringBuilder("数据点统计信息:\n");
        sb.append("总数据点: ").append(totalDataPoints.get()).append("\n");
        sb.append("设备数据点统计:\n");

        deviceDataPointsCount.forEach((deviceId, count) -> {
            sb.append("  设备[")
              .append(deviceId)
              .append("]: ")
              .append(count.get())
              .append("个数据点\n");
        });

        String statistics = sb.toString();
        LogUtil.info(log, "aggregator.statistics", statistics);
        getProbe().enqueueWindowLogItem(AGGREGATOR_LOG, new ActorLogItem(ActorLogLevel.INFO, statistics));
    }

}