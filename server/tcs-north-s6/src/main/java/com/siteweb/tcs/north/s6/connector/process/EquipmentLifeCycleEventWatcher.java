package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorProbe;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 设备生命周期时间监听
 */
public class EquipmentLifeCycleEventWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    public static Props props() {
        return Props.create(EquipmentLifeCycleEventWatcher.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .build();
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }
}

