package com.siteweb.tcs.north.s6.connector.protocol;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-08-09)
 **/
@Data
public class DistributeControl {

    @JsonProperty("StationId")
    private Integer stationId;


    @JsonProperty("HostId")
    private Integer hostId;

    @JsonProperty("EquipmentId")
    private Integer equipmentId;

    @JsonProperty("ControlId")
    private Integer controlId;

    @JsonProperty("BaseTypeId")
    private Long baseTypeId;

    @JsonProperty("UserId")
    private Integer userId;

    @JsonProperty("SerialNo")
    private Integer serialNo;

    @JsonProperty("ControlType")
    private Integer controlType;

    @JsonProperty("Priority")
    private Integer priority;

    @JsonProperty("ParameterValues")
    private String parameterValues;


    @JsonProperty("StartTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime startTime;
}
