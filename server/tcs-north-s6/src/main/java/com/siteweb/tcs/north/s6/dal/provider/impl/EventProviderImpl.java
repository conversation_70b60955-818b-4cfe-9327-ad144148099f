package com.siteweb.tcs.north.s6.dal.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import com.siteweb.tcs.north.s6.dal.entity.EventResponseItem;
import com.siteweb.tcs.north.s6.dal.mapper.EventMapper;
import com.siteweb.tcs.north.s6.dal.provider.EquipmentProvider;
import com.siteweb.tcs.north.s6.dal.provider.EventProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class EventProviderImpl implements EventProvider {
    @Autowired
    private EventMapper eventMapper;
    @Autowired
    private EquipmentProvider equipmentProvider;

    public boolean saveEventResponse(List<EventResponseItem> itemList){
        if(CollectionUtil.isEmpty(itemList)) return false;
        boolean ret = false;
        try {
            itemList.forEach(item ->{
                Map<String,Object> params = new HashMap<>();
                params.put("stationId", item.getStationId());
                params.put("equipmentId", item.getEquipmentId());
                params.put("eventId", item.getEventId());
                params.put("eventConditionId", item.getEventConditionId());
                params.put("sequenceId", item.getSequenceId());
                params.put("startTime", item.getStartTime());
                params.put("endTime", item.getEndTime());
                params.put("overturn", item.getOverturn());
                params.put("meanings", item.getMeanings());
                params.put("eventValue", item.getEventValue());
                params.put("baseTypeId", item.getBaseTypeId());
                params.put("ret", null); // Placeholder for OUT parameter
                Integer retInt = eventMapper.saveEventResponse(params);
                log.info("[SAVE EVENT RESPONSE] save event to database successfully, the save result is {}.",retInt);
            });
            ret = true;
        }catch (Exception ex){
            log.error("[SAVE EVENT RESPONSE] call PNL_SaveEventResponse to save event error, the error reason is {}.",ex.getCause());
            ret = false;
        }
        return ret;
    }

    @Override
    public Pair<List<Integer>,List<Integer>> deviceSelfDiagnosis(List<EventResponseItem> itemList){
        if(CollectionUtil.isEmpty(itemList))return Pair.of(null,null);
        List<Integer> offlineList = itemList.stream().filter(e -> e.getEventId() == -3 && ObjectUtil.isNotEmpty(e.getStartTime()) && ObjectUtil.isEmpty(e.getEndTime()))
                .map(EventResponseItem::getEquipmentId).toList();
        List<Integer> onlineList = itemList.stream().filter(e -> e.getEventId() == -3 && ObjectUtil.isNotEmpty(e.getStartTime()) && ObjectUtil.isNotEmpty(e.getEndTime()))
                .map(EventResponseItem::getEquipmentId).toList();
        equipmentProvider.updateConnectState(offlineList, EnumDeviceConnectState.OFFLINE.getCode());
        equipmentProvider.updateConnectState(onlineList,EnumDeviceConnectState.ONLINE.getCode());
        Pair<List<Integer>,List<Integer>> res = Pair.of(offlineList,onlineList);
        return res;
    }
}
