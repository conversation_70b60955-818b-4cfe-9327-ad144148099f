package com.siteweb.tcs.north.s6.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * @ClassName: RealTimeDataRedisProperties
 * @descriptions: 实时数据redis配置参数，只用于S6北向插件
 * S6中redis分库，用于分隔实时数据业务与正常逻辑业务
 * 场景不同：
 * 实时数据redis：无需持久化，用于高频写入场景，避免过高磁盘io造成性能下降
 * 正常业务redis：存储指标，用户登录token，自诊断信息，这些使用rdb持久化，重启可恢复
 * @author: xsx
 * @date: 2024/8/2 9:20
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "siteweb.real-time-data.redis")
public class RealTimeDataRedisProperties {
    private int database = 0;

    private String host;

    /**
     * Login password of the redis server.
     */
    private String password;

    /**
     * Redis server port.
     */
    private int port;

    /**
     * Read timeout.
     */
    private Duration timeout;

    /**
     * Connection timeout.
     */
    private Duration connectTimeout;
}
